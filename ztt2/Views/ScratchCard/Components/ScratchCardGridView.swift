//
//  ScratchCardGridView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡网格视图组件
 * 实现多张刮刮卡的网格布局和单卡显示
 */
struct ScratchCardGridView: View {
    
    // MARK: - Properties
    let cardItems: [ScratchCardItem]
    let onCardTapped: (Int) -> Void
    
    // MARK: - Layout Properties
    private let columns = Array(repeating: GridItem(.flexible(), spacing: 16), count: 3)
    private let itemSize = CGSize(width: 100, height: 140)
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(Array(cardItems.enumerated()), id: \.element.id) { index, cardItem in
                scratchCardItemView(cardItem: cardItem, index: index)
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Scratch Card Item View
    
    /**
     * 单张刮刮卡项目视图
     */
    private func scratchCardItemView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 8) {
            // 刮刮卡主体
            ScratchCardItemView(
                cardItem: cardItem,
                size: itemSize,
                onTap: {
                    onCardTapped(index)
                }
            )
            .floating(
                offset: CGFloat.random(in: 4...8),
                rotation: Double.random(in: 1...4),
                delay: Double(index) * 0.2,
                duration: Double.random(in: 2.0...2.8),
                enabled: !cardItem.isScratched && cardItem.animationState == .idle
            )
            
            // 刮刮卡信息
            cardInfoView(cardItem: cardItem, index: index)
        }
        .contentShape(Rectangle()) // 扩大点击区域
    }
    
    /**
     * 卡片信息视图
     */
    private func cardInfoView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 4) {
            // 卡片标题
            Text(cardItem.displayTitle)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            // 状态指示器
            statusIndicator(for: cardItem)
        }
    }
    
    /**
     * 状态指示器
     */
    private func statusIndicator(for cardItem: ScratchCardItem) -> some View {
        HStack(spacing: 4) {
            // 状态图标
            Image(systemName: statusIcon(for: cardItem))
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(statusColor(for: cardItem))
            
            // 状态文字
            Text(statusText(for: cardItem))
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(statusColor(for: cardItem))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(statusColor(for: cardItem).opacity(0.1))
        )
    }
    
    /**
     * 获取状态图标
     */
    private func statusIcon(for cardItem: ScratchCardItem) -> String {
        if cardItem.isScratched {
            return "checkmark.circle.fill"
        } else if cardItem.isClickable {
            return "hand.tap.fill"
        } else {
            return "clock.fill"
        }
    }
    
    /**
     * 获取状态文字
     */
    private func statusText(for cardItem: ScratchCardItem) -> String {
        if cardItem.isScratched {
            return "scratch_card.status.scratched".localized
        } else if cardItem.isClickable {
            return "scratch_card.status.available".localized
        } else {
            return "等待中"
        }
    }
    
    /**
     * 获取状态颜色
     */
    private func statusColor(for cardItem: ScratchCardItem) -> Color {
        if cardItem.isScratched {
            return Color.green
        } else if cardItem.isClickable {
            return Color(hex: "#a9d051")
        } else {
            return Color.gray
        }
    }
}

/**
 * 单张刮刮卡项目视图
 */
struct ScratchCardItemView: View {
    
    // MARK: - Properties
    let cardItem: ScratchCardItem
    let size: CGSize
    let onTap: () -> Void
    
    // MARK: - State Properties
    @State private var isPressed = false
    
    var body: some View {
        ZStack {
            // 卡片背景
            cardBackground
            
            // 卡片内容
            cardContent
            
            // 刮除状态覆盖层
            if cardItem.isScratched {
                scratchedOverlay
            }
        }
        .frame(width: size.width, height: size.height)
        .scaleEffect(cardItem.scaleEffect * (isPressed ? 0.95 : 1.0))
        .opacity(cardItem.opacity)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .animation(.easeInOut(duration: 0.6), value: cardItem.animationState)
        .onTapGesture {
            if cardItem.isClickable {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                onTap()
            }
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing && cardItem.isClickable
        }, perform: {})
    }
    
    // MARK: - Card Background
    
    private var cardBackground: some View {
        ZStack {
            // 主背景
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(hex: "#ffeef0"),
                            Color(hex: "#fff8e1")
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 边框
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: cardItem.isScratched ? [
                            Color.green.opacity(0.4),
                            Color.green.opacity(0.2)
                        ] : [
                            Color(hex: "#ff6b6b").opacity(0.3),
                            Color(hex: "#a9d051").opacity(0.3)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        }
        .shadow(
            color: cardItem.isScratched ? 
                Color.green.opacity(0.2) : Color.black.opacity(0.12),
            radius: cardItem.isScratched ? 8 : 6,
            x: 0,
            y: cardItem.isScratched ? 4 : 3
        )
    }
    
    // MARK: - Card Content
    
    private var cardContent: some View {
        VStack(spacing: 8) {
            // 刮刮卡图标
            if cardItem.isScratched {
                prizeContent
            } else {
                scratchableContent
            }
        }
        .padding(12)
    }
    
    /**
     * 奖品内容
     */
    private var prizeContent: some View {
        VStack(spacing: 8) {
            // 中奖图标
            Image("刮刮卡中奖")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 40, height: 40)
            
            // 奖品名称
            Text(cardItem.prizeName)
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
    }
    
    /**
     * 可刮除内容
     */
    private var scratchableContent: some View {
        VStack(spacing: 8) {
            // 刮刮卡图标
            Image("刮刮卡")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 40, height: 40)
            
            // 提示文字
            Text("scratch_card.hint_text".localized)
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }
    
    /**
     * 刮除状态覆盖层
     */
    private var scratchedOverlay: some View {
        ZStack {
            // 成功背景
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.green.opacity(0.1))
            
            // 成功图标
            VStack(spacing: 4) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(Color.green)
                
                Text("scratch_card.status.won".localized)
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(Color.green)
            }
        }
    }
}

#Preview {
    let sampleCards = [
        ScratchCardItem.create(index: 0, prizeName: "小贴纸"),
        ScratchCardItem.create(index: 1, prizeName: "铅笔"),
        ScratchCardItem.create(index: 2, prizeName: "橡皮"),
        ScratchCardItem.create(index: 3, prizeName: "尺子"),
        ScratchCardItem.create(index: 4, prizeName: "谢谢参与"),
        ScratchCardItem.create(index: 5, prizeName: "小玩具")
    ]
    
    return ScratchCardGridView(
        cardItems: sampleCards,
        onCardTapped: { index in
            print("Tapped card \(index)")
        }
    )
    .padding()
}
